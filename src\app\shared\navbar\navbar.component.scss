.mat-toolbar {
  background: #181a20 !important;
  color: #fff !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.25);
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
  letter-spacing: 1px;
  color: #90caf9;
}

.spacer {
  flex: 1 1 auto;
}

a[mat-button] {
  font-size: 1rem;
  margin-left: 8px;
  text-transform: uppercase;
  color: #fff !important;
  opacity: 0.85;
  transition: color 0.2s, opacity 0.2s;
}

a.active-link {
  font-weight: bold;
  border-bottom: 2px solid #90caf9;
  color: #90caf9 !important;
  opacity: 1;
}

@media (max-width: 600px) {
  .mat-toolbar {
    flex-direction: column;
    align-items: flex-start;
  }
  a[mat-button] {
    margin-left: 0;
    margin-top: 4px;
  }
}
