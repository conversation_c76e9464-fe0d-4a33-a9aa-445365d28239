.home-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 64px - 40px); // Subtract navbar (~64px) and footer (~40px)
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.bg-image {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  width: 100%;
  height: 100%;
  background: url('/assets/images/c2c371ac-e904-4dc0-be51-cb67e09fc5b0.png') center center/cover no-repeat;
  opacity: 0.18;
  z-index: 1;
  filter: blur(1.5px) brightness(0.9);
  pointer-events: none;
}

.bg-overlay {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  background: linear-gradient(180deg, rgba(0,0,0,0.45) 0%, rgba(0,0,0,0.65) 100%);
  pointer-events: none;
}

.home-content {
  position: relative;
  z-index: 3;
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1.5rem;
  box-sizing: border-box;
}

.big-welcome {
  font-size: 2.8rem;
  font-weight: 800;
  color: #fff;
  text-align: center;
  margin-bottom: 2.2rem;
  letter-spacing: 1.5px;
  line-height: 1.15;
  text-shadow: 0 2px 24px rgba(0,0,0,0.45);
}

.info-list {
  margin-bottom: 2.2rem;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.7rem;
}

.info-line {
  font-size: 1.15rem;
  color: #e0e0e0;
  border-radius: 8px;
  padding: 0.5rem 1.2rem;
  box-shadow: 0 1px 6px rgba(0,0,0,0.12);
  font-weight: 500;
}

.cta-row {
  display: flex;
  gap: 1.5rem;
  margin-top: 1.5rem;
  justify-content: center;
}

.contact-link {
  font-size: 1rem;
  text-decoration: underline;
  color: #90caf9 !important;
}

@media (max-width: 700px) {
  .home-container {
    height: calc(100vh - 80px - 35px); // Adjusted for mobile navbar and footer
  }

  .home-content {
    padding: 1.5rem 1rem;
    max-width: 100%;
  }

  .big-welcome {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    line-height: 1.2;
  }

  .info-line {
    font-size: 1rem;
    padding: 0.4rem 0.8rem;
  }

  .cta-row {
    flex-direction: column;
    gap: 1rem;
    align-items: center;

    button, a {
      width: 100%;
      max-width: 250px;
      text-align: center;
    }
  }
}

@media (max-width: 480px) {
  .home-container {
    height: calc(100vh - 90px - 30px); // Further adjusted for very small screens
  }

  .home-content {
    padding: 1rem 0.75rem;
  }

  .big-welcome {
    font-size: 1.5rem;
    margin-bottom: 1.2rem;
  }

  .info-line {
    font-size: 0.9rem;
    padding: 0.3rem 0.6rem;
  }
}
