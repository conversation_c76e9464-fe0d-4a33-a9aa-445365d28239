﻿.about-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem 1rem 3rem 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.timeline {
  width: 100%;
  margin-bottom: 2.5rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
}

.timeline-card {
  position: relative;
  margin-bottom: 2rem;
  padding-left: 2.5rem;
  min-width: 250px;
  background: rgba(24,26,32,0.85) !important;
  color: #e0e0e0 !important;
  box-shadow: none !important;
  border-radius: 12px;
}

.timeline-dot {
  position: absolute;
  left: 0.7rem;
  top: 1.2rem;
  width: 1.1rem;
  height: 1.1rem;
  background: #1976d2;
  border-radius: 50%;
  border: 3px solid #23272f;
  box-shadow: 0 0 0 2px #1976d2;
}

.timeline-content h2 {
  margin-top: 0;
  margin-bottom: 0.3rem;
  color: #90caf9;
}

.vision-mission {
  display: flex;
  gap: 2rem;
  width: 100%;
  margin-bottom: 2.5rem;
  flex-wrap: wrap;
  justify-content: center;
}

.vision-card, .mission-card {
  flex: 1 1 250px;
  min-width: 220px;
  max-width: 350px;
  background: rgba(24,26,32,0.85) !important;
  color: #e0e0e0 !important;
  box-shadow: none !important;
  border-radius: 12px;
}

.locations {
  width: 100%;
  margin-top: 1.5rem;
}

.location-list {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 1rem;
}

.location-card {
  display: flex;
  align-items: flex-start;
  gap: 0.7rem;
  min-width: 200px;
  max-width: 300px;
  padding: 1rem 1.2rem;
  background: rgba(24,26,32,0.85) !important;
  color: #e0e0e0 !important;
  box-shadow: none !important;
  border-radius: 12px;
}

.location-card mat-icon {
  color: #90caf9;
  font-size: 2rem;
  margin-top: 0.2rem;
}

.loc-name {
  font-weight: 600;
  color: #fff;
}

.loc-address {
  font-size: 0.97rem;
  color: #b0b0b0;
}

@media (max-width: 700px) {
  .vision-mission {
    flex-direction: column;
    gap: 1rem;
  }
  .location-list {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }
}
