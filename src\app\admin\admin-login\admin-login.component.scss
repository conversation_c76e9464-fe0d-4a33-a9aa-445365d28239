.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 64px - 40px);
  padding: 2rem 1rem;
  background: linear-gradient(135deg, #181a20 0%, #23272f 100%);
}


.login-card {
  background: #23272f;
  border-radius: 16px;
  padding: 3rem 2.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 400px;
  border: 1px solid rgba(36, 52, 65, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
  
  h1 {
    color: #90caf9;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: #b0b0b0;
    font-size: 1rem;
    margin: 0;
  }
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.full-width {
  width: 100%;
}

.mat-mdc-form-field {
  .mdc-text-field--outlined {
    .mdc-notched-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: rgba(144, 202, 249, 0.3) !important;
      }
    }
    
    &:hover .mdc-notched-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: rgba(144, 202, 249, 0.6) !important;
      }
    }
    
    &.mdc-text-field--focused .mdc-notched-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: #90caf9 !important;
        border-width: 2px !important;
      }
    }
  }
}

.error-message {
  color: #ff8a80;
  font-size: 0.9rem;
  text-align: center;
  padding: 0.5rem;
  background: rgba(255, 138, 128, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(255, 138, 128, 0.3);
}

.login-button {
  background: #90caf9 !important;
  color: #181a20 !important;
  font-weight: 600;
  padding: 0.75rem 2rem;
  border-radius: 8px;
  font-size: 1rem;
  margin-top: 1rem;
  
  &:disabled {
    background: rgba(144, 202, 249, 0.3) !important;
    color: rgba(24, 26, 32, 0.5) !important;
  }
}
  
  code {
    background: rgba(144, 202, 249, 0.1);
    color: #90caf9;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
  }


@media (max-width: 480px) {
  .login-card {
    padding: 2rem 1.5rem;
    margin: 1rem;
  }
  
  .login-header h1 {
    font-size: 1.5rem;
  }
}
