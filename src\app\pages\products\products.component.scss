.products-container {
  max-width: 1100px;
  margin: 0 auto;
  padding: 2rem 1rem 3rem 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 2rem;
  width: 100%;
  margin-top: 2rem;
}

.product-card {
  max-width: 320px;
  min-width: 220px;
  margin: 0 auto;
  box-shadow: 0 2px 12px rgba(0,0,0,0.25);
  transition: transform 0.2s cubic-bezier(.4,2,.6,1);
  border-radius: 12px;
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: #23272f !important;
  color: #e0e0e0 !important;
}

.product-card:hover {
  transform: translateY(-6px) scale(1.04);
  box-shadow: 0 6px 24px rgba(25, 118, 210, 0.13);
}

img[mat-card-image] {
  width: 100%;
  aspect-ratio: 4/3;
  object-fit: cover;
  border-radius: 12px 12px 0 0;
  background: #181a20;
  height: 240px;
  transition: opacity 0.3s ease;

  // Loading state
  &:not([src]), &[src=""] {
    opacity: 0.5;
    background: #181a20 url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2390caf9"><path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/></svg>') center center/50px no-repeat;
  }

  // Error state
  &[src*="placeholder"] {
    background: #181a20 url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ff8a80"><path d="M21 5v6.59l-3-3.01-4 4.01-4-4-4 4-3-3.01V5c0-1.1.9-2 2-2h14c1.1 0 2 .9 2 2zm-3 6.42l3 3.01V19c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2v-6.58l3 2.99 4-4 4 4 4-3.99z"/></svg>') center center/50px no-repeat;
    opacity: 0.7;
  }
}

mat-card-title {
  color: #90caf9;
  font-weight: 600;
  font-size: 1.15rem;
  margin: 0.5rem 0 0.2rem 0;
  padding: 0 0.7rem;
}

mat-card-content p {
  color: #b0b0b0;
  font-size: 1.05rem;
  margin: 0 0 0.7rem 0;
  padding: 0 0.7rem;
}

@media (max-width: 700px) {
  .product-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
  }
  .product-card {
    max-width: 98vw;
    min-width: 0;
  }
  img[mat-card-image] {
    height: 140px;
  }
}
