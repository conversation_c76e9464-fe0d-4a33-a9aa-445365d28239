import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ProductService, Product } from '../../shared/product.service';
import { AdminAuthService } from '../services/admin-auth.service';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-manage-photos',
  templateUrl: './manage-photos.component.html',
  styleUrls: ['./manage-photos.component.scss'],
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(40px)' }),
        animate('700ms 100ms cubic-bezier(.4,2,.6,1)', style({ opacity: 1, transform: 'none' }))
      ])
    ]),
    trigger('slideIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateX(-20px)' }),
        animate('500ms 200ms cubic-bezier(.4,2,.6,1)', style({ opacity: 1, transform: 'none' }))
      ])
    ])
  ]
})
export class ManagePhotosComponent implements OnInit {
  products: Product[] = [];
  showAddForm = false;
  editingProduct: Product | null = null;
  productForm: FormGroup;
  selectedFile: File | null = null;
  previewUrl: string | null = null;

  constructor(
    private productService: ProductService,
    private adminAuthService: AdminAuthService,
    private router: Router,
    private fb: FormBuilder
  ) {
    this.productForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      caption: ['', [Validators.required, Validators.minLength(5)]],
      imageUrl: ['', [Validators.required]]
    });
  }

  ngOnInit() {
    this.loadProducts();
  }

  loadProducts() {
    this.productService.getProducts().subscribe(products => {
      this.products = products;
    });
  }

  async logout() {
    await this.adminAuthService.logout();
    this.router.navigate(['/admin/login']);
  }

  toggleAddForm() {
    this.showAddForm = !this.showAddForm;
    this.editingProduct = null;
    this.resetForm();
  }

  editProduct(product: Product) {
    this.editingProduct = product;
    this.showAddForm = true;
    this.productForm.patchValue({
      name: product.name,
      caption: product.caption,
      imageUrl: product.imageUrl
    });
    this.previewUrl = product.imageUrl;
  }

  async deleteProduct(productId: string) {
    if (confirm('Are you sure you want to delete this product?')) {
      try {
        // Find the product to get its image URL
        const product = this.products.find(p => p.id === productId);
        await this.productService.deleteProductWithImage(productId, product?.imageUrl);
        this.loadProducts();
      } catch (error) {
        console.error('Error deleting product:', error);
        alert('Error deleting product. Please try again.');
      }
    }
  }

  onFileSelected(event: any) {
    const file = event.target.files[0];
    if (file) {
      this.selectedFile = file;

      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.previewUrl = e.target.result;
      };
      reader.readAsDataURL(file);

      // Clear the imageUrl field as it will be set after upload to Firebase
      this.productForm.patchValue({ imageUrl: '' });
    }
  }

  async onSubmit() {
    if (this.productForm.valid) {
      const formData = this.productForm.value;

      try {
        if (this.editingProduct) {
          // Update existing product
          const updatedProduct: Product = {
            ...this.editingProduct,
            ...formData
          };
          await this.productService.updateProductWithImage(updatedProduct, this.selectedFile);
        } else {
          // Add new product
          const newProduct = {
            name: formData.name,
            caption: formData.caption,
            imageUrl: formData.imageUrl || '' // Will be updated after image upload
          };
          await this.productService.addProductWithImage(newProduct, this.selectedFile);
        }

        this.loadProducts();
        this.resetForm();
        this.showAddForm = false;
      } catch (error) {
        console.error('Error saving product:', error);
        // You can add user-friendly error handling here
        alert('Error saving product. Please try again.');
      }
    }
  }

  resetForm() {
    this.productForm.reset();
    this.selectedFile = null;
    this.previewUrl = null;
    this.editingProduct = null;
  }

  cancelEdit() {
    this.resetForm();
    this.showAddForm = false;
  }

  get name() { return this.productForm.get('name'); }
  get caption() { return this.productForm.get('caption'); }
  get imageUrl() { return this.productForm.get('imageUrl'); }
}
